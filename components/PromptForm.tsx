import React, { useState, useEffect } from 'react';
import { Prompt } from '../types';
import TagInput from './TagInput';

interface PromptFormProps {
  promptToEdit?: Prompt | null; 
  onSave: (promptData: Omit<Prompt, 'id' | 'originalPromptId' | 'version' | 'createdAt' | 'lastUsedAt' | 'timesUsed'>, existingPrompt?: Prompt | null) => void;
  onClose: () => void;
}

const PromptForm: React.FC<PromptFormProps> = ({ promptToEdit, onSave, onClose }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [promptText, setPromptText] = useState('');
  const [tags, setTags] = useState<string[]>([]);

  useEffect(() => {
    if (promptToEdit) {
      setTitle(promptToEdit.title);
      setDescription(promptToEdit.description);
      setPromptText(promptToEdit.promptText);
      setTags(promptToEdit.tags);
    }
  }, [promptToEdit]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !promptText.trim()) {
      alert("Title and Prompt Text are required.");
      return;
    }

    const promptData = {
      title: title.trim(),
      description: description.trim(),
      promptText: promptText.trim(),
      tags,
    };
    onSave(promptData, promptToEdit);
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="title" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300">
          Title
        </label>
        <input
          type="text"
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          required
          className="mt-1 block w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
        />
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300">
          Short Description (Optional)
        </label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          rows={2}
          className="mt-1 block w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
        />
      </div>

      <div>
        <label htmlFor="promptText" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300">
          Prompt Text
          <span className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">(Use &#123;&#123;variable_name&#125;&#125; for templates)</span>
        </label>
        <textarea
          id="promptText"
          value={promptText}
          onChange={(e) => setPromptText(e.target.value)}
          rows={6}
          required
          className="mt-1 block w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
          placeholder="Enter your AI prompt. Example: Write a blog post about {{topic}}."
        />
      </div>
      
      <TagInput tags={tags} onTagsChange={setTags} />

      <div className="flex justify-end space-x-3 pt-2">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-neutral-700 dark:text-neutral-200 bg-neutral-100 dark:bg-neutral-600 border border-neutral-300 dark:border-neutral-500 rounded-md shadow-sm hover:bg-neutral-200 dark:hover:bg-neutral-500 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-neutral-800 focus:ring-primary-light"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-primary-dark border border-transparent rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-neutral-800 focus:ring-primary"
        >
          {promptToEdit ? 'Save as New Version' : 'Create Prompt'}
        </button>
      </div>
    </form>
  );
};

